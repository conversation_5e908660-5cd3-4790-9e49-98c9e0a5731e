/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Floating Hearts Background */
.hearts-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.heart {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #ff6b9d;
    transform: rotate(-45deg);
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

.heart:before,
.heart:after {
    content: '';
    width: 20px;
    height: 20px;
    position: absolute;
    left: 10px;
    top: 0;
    background: #ff6b9d;
    border-radius: 10px;
    transform: rotate(-45deg);
    transform-origin: 0 10px;
}

.heart:after {
    left: 0;
    transform: rotate(45deg);
    transform-origin: 10px 10px;
}

.heart:nth-child(1) { left: 10%; animation-delay: 0s; }
.heart:nth-child(2) { left: 20%; animation-delay: 1s; }
.heart:nth-child(3) { left: 30%; animation-delay: 2s; }
.heart:nth-child(4) { left: 40%; animation-delay: 3s; }
.heart:nth-child(5) { left: 60%; animation-delay: 1.5s; }
.heart:nth-child(6) { left: 70%; animation-delay: 2.5s; }
.heart:nth-child(7) { left: 80%; animation-delay: 0.5s; }
.heart:nth-child(8) { left: 90%; animation-delay: 3.5s; }

@keyframes float {
    0%, 100% { transform: translateY(100vh) rotate(-45deg); opacity: 0; }
    10%, 90% { opacity: 0.7; }
    50% { transform: translateY(-10vh) rotate(-45deg); }
}

/* Container */
.container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Welcome Section */
.welcome-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.welcome-content {
    background: rgba(255, 255, 255, 0.9);
    padding: 60px 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: fadeInUp 1s ease-out;
}

.main-title {
    font-family: 'Dancing Script', cursive;
    font-size: 4rem;
    color: #d63384;
    margin-bottom: 10px;
    animation: pulse 2s ease-in-out infinite;
}

.sub-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: #e91e63;
    margin-bottom: 20px;
}

.welcome-text {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

.start-btn {
    background: linear-gradient(45deg, #ff6b9d, #e91e63);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(233, 30, 99, 0.3);
}

.start-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(233, 30, 99, 0.4);
}

/* Hidden class */
.hidden {
    display: none;
}

/* Card Section */
.card-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.birthday-card {
    width: 600px;
    height: 400px;
    position: relative;
    cursor: pointer;
    perspective: 1000px;
    margin: 0 auto;
}

.card-front,
.card-inside {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 15px;
    transition: transform 0.8s ease;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.card-front {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.card-inside {
    background: white;
    transform: rotateY(180deg);
    display: flex;
    padding: 30px;
}

.birthday-card.flipped .card-front {
    transform: rotateY(-180deg);
}

.birthday-card.flipped .card-inside {
    transform: rotateY(0deg);
}

.card-decoration {
    position: absolute;
    top: 20px;
    width: 100%;
}

.balloon {
    position: absolute;
    font-size: 2rem;
    animation: bounce 2s ease-in-out infinite;
}

.balloon-1 { left: 20%; animation-delay: 0s; }
.balloon-2 { left: 50%; animation-delay: 0.5s; }
.balloon-3 { right: 20%; animation-delay: 1s; }

.gift-box {
    font-size: 4rem;
    margin: 20px 0;
    animation: rotate 3s ease-in-out infinite;
}

.card-left {
    flex: 1;
    padding-right: 20px;
}

.card-right {
    flex: 1;
    padding-left: 20px;
}

.card-left h3 {
    color: #d63384;
    font-family: 'Dancing Script', cursive;
    font-size: 1.8rem;
    margin-bottom: 15px;
}

.card-left p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.signature {
    font-family: 'Dancing Script', cursive;
    font-size: 1.3rem;
    color: #e91e63;
    text-align: right;
}

.photo-frame {
    background: #f8f9fa;
    border: 3px solid #e91e63;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.photo-placeholder {
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
}

.heart-icon {
    font-size: 2rem;
    margin-top: 10px;
}

.wishes p {
    color: #666;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes rotate {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

/* Section Titles */
.section-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    color: #d63384;
    text-align: center;
    margin-bottom: 40px;
    animation: fadeInUp 1s ease-out;
}

/* Messages Section */
.messages-section {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.messages-container {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
    justify-content: center;
}

.message-card {
    width: 200px;
    height: 250px;
    position: relative;
    cursor: pointer;
    perspective: 1000px;
}

.message-front,
.message-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 15px;
    transition: transform 0.6s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.message-front {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.message-back {
    background: white;
    color: #333;
    transform: rotateY(180deg);
}

.message-card.flipped .message-front {
    transform: rotateY(-180deg);
}

.message-card.flipped .message-back {
    transform: rotateY(0deg);
}

.message-number {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 10px;
}

/* Cake Section */
.cake-section {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.cake-container {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

#cake-holder {
    position: relative;
    margin: 0 auto;
    width: 400px;
    height: 400px;
}

/* Beautiful Cake Design */
.cake {
    position: absolute;
    width: 250px;
    height: 200px;
    top: 50%;
    left: 50%;
    margin-top: -70px;
    margin-left: -125px;
}

.plate {
    width: 270px;
    height: 110px;
    position: absolute;
    bottom: -10px;
    left: -10px;
    background-color: #ccc;
    border-radius: 50%;
    box-shadow:
        0 2px 0 #b3b3b3,
        0 4px 0 #b3b3b3,
        0 5px 40px rgba(0, 0, 0, 0.5);
}

.cake > * {
    position: absolute;
}

.layer {
    position: absolute;
    display: block;
    width: 250px;
    height: 100px;
    border-radius: 50%;
    background-color: #553c13;
    box-shadow:
        0 2px 0px #5a4015,
        0 4px 0px #4d3510,
        0 6px 0px #4c3410,
        0 8px 0px #4b340f,
        0 10px 0px #4a330f,
        0 12px 0px #49330e,
        0 14px 0px #48320e,
        0 16px 0px #47320d,
        0 18px 0px #46310d,
        0 20px 0px #45310c,
        0 22px 0px #44300c,
        0 24px 0px #43300b,
        0 26px 0px #422f0b,
        0 28px 0px #412f0a,
        0 30px 0px #402e0a;
}

.layer-top { top: 0px; }
.layer-middle { top: 33px; }
.layer-bottom { top: 66px; }

.icing {
    top: 2px;
    left: 5px;
    background-color: #f0e4d0;
    width: 240px;
    height: 90px;
    border-radius: 50%;
}

.icing:before {
    content: "";
    position: absolute;
    top: 4px;
    right: 5px;
    bottom: 6px;
    left: 5px;
    background-color: #f3e7d3;
    box-shadow:
        0 0 4px #f6ead6,
        0 0 4px #f6ead6,
        0 0 4px #f6ead6;
    border-radius: 50%;
    z-index: 1;
}

.drip {
    display: block;
    width: 50px;
    height: 60px;
    border-bottom-left-radius: 25px;
    border-bottom-right-radius: 25px;
    background-color: #f0e4d0;
}

.drip1 {
    top: 53px;
    left: 5px;
    transform: skewY(15deg);
    height: 48px;
    width: 40px;
}

.drip2 {
    top: 69px;
    left: 181px;
    transform: skewY(-15deg);
}

.drip3 {
    top: 54px;
    left: 90px;
    width: 80px;
    border-bottom-left-radius: 40px;
    border-bottom-right-radius: 40px;
}

.candle {
    background-color: #7B020B;
    width: 16px;
    height: 50px;
    border-radius: 8px / 4px;
    top: -20px;
    z-index: 10;
}

.candle:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 16px;
    height: 8px;
    border-radius: 50%;
    background-color: #8f0c15;
}

/* Position individual candles */
.candle1 {
    left: 35%;
    margin-left: -8px;
}

.candle2 {
    left: 50%;
    margin-left: -8px;
}

.candle3 {
    left: 65%;
    margin-left: -8px;
}

.flame {
    position: absolute;
    background-color: orange;
    width: 15px;
    height: 35px;
    border-radius: 10px 10px 10px 10px / 25px 25px 10px 10px;
    top: -34px;
    left: 50%;
    margin-left: -7.5px;
    z-index: 10;
    cursor: pointer;
    box-shadow:
        0 0 10px rgba(255, 165, 0, 0.5),
        0 0 20px rgba(255, 165, 0, 0.5),
        0 0 60px rgba(255, 165, 0, 0.5),
        0 0 80px rgba(255, 165, 0, 0.5);
    transform-origin: 50% 90%;
    animation: flicker 1s ease-in-out alternate infinite;
    transition: all 0.3s ease;
}

.flame:hover {
    transform: scale(1.1);
}

.flame.blown-out {
    opacity: 0;
    transform: scale(0);
}

.flame.blown-out::after {
    content: '💨';
    position: absolute;
    top: -15px;
    left: -8px;
    font-size: 1.2rem;
    opacity: 1;
    animation: smokeRise 1.5s ease-out;
}

@keyframes flicker {
    0% {
        transform: skewX(5deg);
        box-shadow:
            0 0 10px rgba(255, 165, 0, 0.2),
            0 0 20px rgba(255, 165, 0, 0.2),
            0 0 60px rgba(255, 165, 0, 0.2),
            0 0 80px rgba(255, 165, 0, 0.2);
    }
    25% {
        transform: skewX(-5deg);
        box-shadow:
            0 0 10px rgba(255, 165, 0, 0.5),
            0 0 20px rgba(255, 165, 0, 0.5),
            0 0 60px rgba(255, 165, 0, 0.5),
            0 0 80px rgba(255, 165, 0, 0.5);
    }
    50% {
        transform: skewX(10deg);
        box-shadow:
            0 0 10px rgba(255, 165, 0, 0.3),
            0 0 20px rgba(255, 165, 0, 0.3),
            0 0 60px rgba(255, 165, 0, 0.3),
            0 0 80px rgba(255, 165, 0, 0.3);
    }
    75% {
        transform: skewX(-10deg);
        box-shadow:
            0 0 10px rgba(255, 165, 0, 0.4),
            0 0 20px rgba(255, 165, 0, 0.4),
            0 0 60px rgba(255, 165, 0, 0.4),
            0 0 80px rgba(255, 165, 0, 0.4);
    }
    100% {
        transform: skewX(5deg);
        box-shadow:
            0 0 10px rgba(255, 165, 0, 0.5),
            0 0 20px rgba(255, 165, 0, 0.5),
            0 0 60px rgba(255, 165, 0, 0.5),
            0 0 80px rgba(255, 165, 0, 0.5);
    }
}

@keyframes smokeRise {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-40px) scale(1.8);
    }
}

.birthday-message {
    position: absolute;
    bottom: -120px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    text-align: center;
    width: 100%;
    max-width: 500px;
    white-space: nowrap;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: all 1s ease;
    z-index: 10;
}

#cake-holder.done .birthday-message {
    opacity: 1;
    bottom: -100px;
}

.cake-instruction {
    font-size: 1.2rem;
    color: #666;
    margin-top: 20px;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    width: 100%;
}

.wish-message {
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 15px;
    margin-top: 30px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease-out;
}

.wish-message h3 {
    color: #d63384;
    margin-bottom: 15px;
}

/* Final Section */
.final-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.final-content {
    background: rgba(255, 255, 255, 0.9);
    padding: 60px 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    max-width: 600px;
}

.final-content h2 {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    color: #d63384;
    margin-bottom: 20px;
}

.final-content p {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 30px;
}

.celebration-btn {
    background: linear-gradient(45deg, #ff6b9d, #e91e63);
    color: white;
    padding: 15px 30px;
    font-size: 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(233, 30, 99, 0.3);
}

.celebration-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(233, 30, 99, 0.4);
}

/* Confetti */
.confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #ff6b9d;
    animation: confetti-fall 3s linear infinite;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* Background Flowers */
.flowers-background {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
    opacity: 0.3;
}

.flowers-background .flower {
    position: absolute;
    bottom: 0;
    transform-origin: bottom center;
    --fl-speed: 0.8s;
}

.flowers-background .flower--1 {
    left: 10%;
    animation: moving-flower-1 6s linear infinite;
}

.flowers-background .flower--1 .flower__line {
    height: 40vmin;
    animation-delay: 0.3s;
}

.flowers-background .flower--2 {
    left: 80%;
    transform: rotate(20deg);
    animation: moving-flower-2 5s linear infinite;
}

.flowers-background .flower--2 .flower__line {
    height: 35vmin;
    animation-delay: 0.6s;
}

.flowers-background .flower--3 {
    left: 45%;
    transform: rotate(-15deg);
    animation: moving-flower-3 7s linear infinite;
}

.flowers-background .flower--3 .flower__line {
    height: 30vmin;
    animation-delay: 0.9s;
}

.flowers-background .flower__leafs {
    position: relative;
    animation: blooming-flower 3s backwards;
}

.flowers-background .flower__leafs--1 {
    animation-delay: 2s;
}

.flowers-background .flower__leafs--2 {
    animation-delay: 3s;
}

.flowers-background .flower__leafs--3 {
    animation-delay: 4s;
}

.flowers-background .flower__leafs::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    transform: translate(-50%, -100%);
    width: 6vmin;
    height: 6vmin;
    background-color: #ff69b4;
    filter: blur(8vmin);
    opacity: 0.5;
}

.flowers-background .flower__leaf {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 6vmin;
    height: 8vmin;
    border-radius: 51% 49% 47% 53%/44% 45% 55% 69%;
    background-color: #ff69b4;
    background-image: linear-gradient(to top, #ff1493, #ff69b4);
    transform-origin: bottom center;
    opacity: 0.7;
    box-shadow: inset 0 0 1vmin rgba(255, 255, 255, 0.3);
}

.flowers-background .flower__leaf--1 {
    transform: translate(-10%, 1%) rotateY(40deg) rotateX(-50deg);
}

.flowers-background .flower__leaf--2 {
    transform: translate(-50%, -4%) rotateX(40deg);
}

.flowers-background .flower__leaf--3 {
    transform: translate(-90%, 0%) rotateY(45deg) rotateX(50deg);
}

.flowers-background .flower__leaf--4 {
    width: 6vmin;
    height: 6vmin;
    transform-origin: bottom left;
    border-radius: 3vmin 8vmin 3vmin 3vmin;
    transform: translate(0%, 18%) rotateX(70deg) rotate(-43deg);
    background-image: linear-gradient(to top, #ff1493, #ff69b4);
    z-index: 1;
    opacity: 0.6;
}

.flowers-background .flower__white-circle {
    position: absolute;
    left: -2.5vmin;
    top: -2vmin;
    width: 6vmin;
    height: 3vmin;
    border-radius: 50%;
    background-color: #ffff99;
    opacity: 0.8;
}

.flowers-background .flower__white-circle::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 45%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    border-radius: inherit;
    background-color: #ffeb12;
    opacity: 0.9;
}

.flowers-background .flower__line {
    height: 35vmin;
    width: 1vmin;
    background-image:
        linear-gradient(to left, rgba(0, 0, 0, 0.1), transparent, rgba(255, 255, 255, 0.1)),
        linear-gradient(to top, transparent 10%, #228b22, #32cd32);
    box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.3);
    animation: grow-flower-tree 4s backwards;
}

.flowers-background .flower__line__leaf {
    --w: 4vmin;
    --h: calc(var(--w) + 1vmin);
    position: absolute;
    top: 20%;
    left: 90%;
    width: var(--w);
    height: var(--h);
    border-top-right-radius: var(--h);
    border-bottom-left-radius: var(--h);
    background-image: linear-gradient(to top, rgba(34, 139, 34, 0.4), #32cd32);
    opacity: 0.6;
}

.flowers-background .flower__line__leaf--1 {
    transform: rotate(70deg) rotateY(30deg);
    animation: blooming-leaf-right var(--fl-speed) 2.6s backwards;
}

.flowers-background .flower__line__leaf--2 {
    top: 45%;
    transform: rotate(70deg) rotateY(30deg);
    animation: blooming-leaf-right var(--fl-speed) 2.4s backwards;
}

.flowers-background .flower__line__leaf--3,
.flowers-background .flower__line__leaf--4 {
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: var(--h);
    border-bottom-right-radius: var(--h);
    left: -460%;
    top: 12%;
    transform: rotate(-70deg) rotateY(30deg);
    animation: blooming-leaf-left var(--fl-speed) 2.2s backwards;
}

.flowers-background .flower__line__leaf--4 {
    top: 40%;
    animation: blooming-leaf-left var(--fl-speed) 2s backwards;
}



/* Full Screen Flower Celebration Overlay */
.flower-celebration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    z-index: 10000;
    display: none;
    align-items: flex-end;
    justify-content: center;
    overflow: hidden;
    perspective: 1000px;
}

.flower-celebration-overlay.active {
    display: flex;
}

.flower-celebration-overlay .celebration-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10001;
    animation: fadeInScale 2s ease-out;
}

.flower-celebration-overlay .birthday-title {
    font-family: 'Dancing Script', cursive;
    font-size: 5rem;
    background: linear-gradient(45deg, #ff6b9d, #e91e63, #ff9ff3, #ff6b9d);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    animation: gradientShift 3s ease-in-out infinite, textGlow 2s ease-in-out infinite alternate;
    text-shadow: 0 0 30px rgba(255, 107, 157, 0.5);
}

.flower-celebration-overlay .celebration-subtitle {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    background: linear-gradient(45deg, #ff6b9d, #e91e63, #ff9ff3, #ff6b9d);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite reverse, textGlow 2s ease-in-out infinite alternate;
    text-shadow: 0 0 20px rgba(255, 107, 157, 0.3);
}

.flower-celebration-overlay .close-celebration {
    position: absolute;
    top: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    cursor: pointer;
    z-index: 10002;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.flower-celebration-overlay .close-celebration:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Flower Animation Styles for Celebration */
.flower-celebration-overlay .night {
    position: fixed;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    width: 100%;
    height: 100%;
    filter: blur(0.1vmin);
    background-image: radial-gradient(ellipse at top, transparent 0%, #000),
                      radial-gradient(ellipse at bottom, #000, rgba(145, 233, 255, 0.2)),
                      repeating-linear-gradient(220deg, black 0px, black 19px, transparent 19px, transparent 22px),
                      repeating-linear-gradient(189deg, black 0px, black 19px, transparent 19px, transparent 22px),
                      repeating-linear-gradient(148deg, black 0px, black 19px, transparent 19px, transparent 22px),
                      linear-gradient(90deg, #00fffa, #f0f0f0);
}

.flower-celebration-overlay .flowers {
    position: relative;
    transform: scale(0.9);
}

.flower-celebration-overlay .flower {
    position: absolute;
    bottom: 10vmin;
    transform-origin: bottom center;
    z-index: 10;
    --fl-speed: 0.8s;
}

.flower-celebration-overlay .flower--1 {
    animation: moving-flower-1 4s linear infinite;
}

.flower-celebration-overlay .flower--1 .flower__line {
    height: 70vmin;
    animation-delay: 0.3s;
}

.flower-celebration-overlay .flower--2 {
    left: 50%;
    transform: rotate(20deg);
    animation: moving-flower-2 4s linear infinite;
}

.flower-celebration-overlay .flower--2 .flower__line {
    height: 60vmin;
    animation-delay: 0.6s;
}

.flower-celebration-overlay .flower--3 {
    left: 50%;
    transform: rotate(-15deg);
    animation: moving-flower-3 4s linear infinite;
}

.flower-celebration-overlay .flower--3 .flower__line {
    animation-delay: 0.9s;
}

/* Text Animation Keyframes */
@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes textGlow {
    0% {
        text-shadow: 0 0 20px rgba(255, 107, 157, 0.5), 0 0 40px rgba(255, 107, 157, 0.3);
    }
    100% {
        text-shadow: 0 0 30px rgba(255, 107, 157, 0.8), 0 0 60px rgba(255, 107, 157, 0.5);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-title { font-size: 2.5rem; }
    .sub-title { font-size: 1.8rem; }
    .birthday-card { width: 90%; height: 350px; }
    .card-inside { flex-direction: column; }
    .card-left, .card-right { padding: 10px; }
    .messages-container { flex-direction: column; align-items: center; }
    .message-card { width: 250px; }
    .section-title { font-size: 2.5rem; }
    .final-content h2 { font-size: 2.5rem; }
    .flowers-background { opacity: 0.2; transform: scale(0.8); }

    .flower-celebration-overlay .birthday-title {
        font-size: 3rem;
    }

    .flower-celebration-overlay .celebration-subtitle {
        font-size: 2rem;
    }
}

/* Flower Animations */
@keyframes moving-flower-1 {
    0%, 100% { transform: rotate(2deg); }
    50% { transform: rotate(-2deg); }
}

@keyframes moving-flower-2 {
    0%, 100% { transform: rotate(18deg); }
    50% { transform: rotate(14deg); }
}

@keyframes moving-flower-3 {
    0%, 100% { transform: rotate(-18deg); }
    50% { transform: rotate(-20deg) rotateY(-10deg); }
}

@keyframes blooming-leaf-right {
    0% {
        transform-origin: left;
        transform: rotate(70deg) rotateY(30deg) scale(0);
    }
}

@keyframes blooming-leaf-left {
    0% {
        transform-origin: right;
        transform: rotate(-70deg) rotateY(30deg) scale(0);
    }
}

@keyframes grow-flower-tree {
    0% {
        height: 0;
        border-radius: 1vmin;
    }
}

@keyframes blooming-flower {
    0% { transform: scale(0); }
}

/* Flower Animation Styles */
.flower-celebration-overlay .flower__leafs {
    position: relative;
    animation: blooming-flower 2s backwards;
}

.flower-celebration-overlay .flower__leafs--1 {
    animation-delay: 1.1s;
}

.flower-celebration-overlay .flower__leafs--2 {
    animation-delay: 1.4s;
}

.flower-celebration-overlay .flower__leafs--3 {
    animation-delay: 1.7s;
}

.flower-celebration-overlay .flower__leafs::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    transform: translate(-50%, -100%);
    width: 8vmin;
    height: 8vmin;
    background-color: #6bf0ff;
    filter: blur(10vmin);
}

.flower-celebration-overlay .flower__leaf {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 8vmin;
    height: 11vmin;
    border-radius: 51% 49% 47% 53%/44% 45% 55% 69%;
    background-color: #a7ffee;
    background-image: linear-gradient(to top, #54b8aa, #a7ffee);
    transform-origin: bottom center;
    opacity: 0.9;
    box-shadow: inset 0 0 2vmin rgba(255, 255, 255, 0.5);
}

.flower-celebration-overlay .flower__leaf--1 {
    transform: translate(-10%, 1%) rotateY(40deg) rotateX(-50deg);
}

.flower-celebration-overlay .flower__leaf--2 {
    transform: translate(-50%, -4%) rotateX(40deg);
}

.flower-celebration-overlay .flower__leaf--3 {
    transform: translate(-90%, 0%) rotateY(45deg) rotateX(50deg);
}

.flower-celebration-overlay .flower__leaf--4 {
    width: 8vmin;
    height: 8vmin;
    transform-origin: bottom left;
    border-radius: 4vmin 10vmin 4vmin 4vmin;
    transform: translate(0%, 18%) rotateX(70deg) rotate(-43deg);
    background-image: linear-gradient(to top, #39c6d6, #a7ffee);
    z-index: 1;
    opacity: 0.8;
}

.flower-celebration-overlay .flower__white-circle {
    position: absolute;
    left: -3.5vmin;
    top: -3vmin;
    width: 9vmin;
    height: 4vmin;
    border-radius: 50%;
    background-color: #fff;
}

.flower-celebration-overlay .flower__white-circle::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 45%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    border-radius: inherit;
    background-image: repeating-linear-gradient(135deg, rgba(0, 0, 0, 0.03) 0px, rgba(0, 0, 0, 0.03) 1px, transparent 1px, transparent 12px),
                      repeating-linear-gradient(45deg, rgba(0, 0, 0, 0.03) 0px, rgba(0, 0, 0, 0.03) 1px, transparent 1px, transparent 12px),
                      linear-gradient(90deg, #ffeb12, #ffce00);
}

.flower-celebration-overlay .flower__line {
    height: 55vmin;
    width: 1.5vmin;
    background-image: linear-gradient(to left, rgba(0, 0, 0, 0.2), transparent, rgba(255, 255, 255, 0.2)),
                      linear-gradient(to top, transparent 10%, #14757a, #39c6d6);
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.5);
    animation: grow-flower-tree 4s backwards;
}

.flower-celebration-overlay .flower__line__leaf {
    --w: 7vmin;
    --h: calc(var(--w) + 2vmin);
    position: absolute;
    top: 20%;
    left: 90%;
    width: var(--w);
    height: var(--h);
    border-top-right-radius: var(--h);
    border-bottom-left-radius: var(--h);
    background-image: linear-gradient(to top, rgba(20, 117, 122, 0.4), #39c6d6);
}

.flower-celebration-overlay .flower__line__leaf--1 {
    transform: rotate(70deg) rotateY(30deg);
    animation: blooming-leaf-right var(--fl-speed) 1.6s backwards;
}

.flower-celebration-overlay .flower__line__leaf--2 {
    top: 45%;
    transform: rotate(70deg) rotateY(30deg);
    animation: blooming-leaf-right var(--fl-speed) 1.4s backwards;
}

.flower-celebration-overlay .flower__line__leaf--3,
.flower-celebration-overlay .flower__line__leaf--4,
.flower-celebration-overlay .flower__line__leaf--6 {
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: var(--h);
    border-bottom-right-radius: var(--h);
    left: -460%;
    top: 12%;
    transform: rotate(-70deg) rotateY(30deg);
    animation: blooming-leaf-left var(--fl-speed) 1.2s backwards;
}

.flower-celebration-overlay .flower__line__leaf--4 {
    top: 40%;
    animation: blooming-leaf-left var(--fl-speed) 1s backwards;
}

.flower-celebration-overlay .flower__line__leaf--5 {
    top: 0;
    transform-origin: left;
    transform: rotate(70deg) rotateY(30deg) scale(0.6);
    animation: blooming-leaf-right var(--fl-speed) 1.8s backwards;
}

.flower-celebration-overlay .flower__line__leaf--6 {
    top: -2%;
    left: -450%;
    transform-origin: right;
    transform: rotate(-70deg) rotateY(30deg) scale(0.6);
    animation: blooming-leaf-left var(--fl-speed) 2s backwards;
}

@keyframes light-ans {
    0% {
        opacity: 0;
        transform: translateY(0vmin);
    }
    25% {
        opacity: 1;
        transform: translateY(-5vmin) translateX(-2vmin);
    }
    50% {
        opacity: 1;
        transform: translateY(-15vmin) translateX(2vmin);
        filter: blur(0.2vmin);
    }
    75% {
        transform: translateY(-20vmin) translateX(-2vmin);
        filter: blur(0.2vmin);
    }
    100% {
        transform: translateY(-30vmin);
        opacity: 0;
        filter: blur(1vmin);
    }
}

/* Flower Lights */
.flower-celebration-overlay .flower__light {
    position: absolute;
    bottom: 0vmin;
    width: 1vmin;
    height: 1vmin;
    background-color: #fffb00;
    border-radius: 50%;
    filter: blur(0.2vmin);
    animation: light-ans 4s linear infinite backwards;
}

.flower-celebration-overlay .flower__light:nth-child(odd) {
    background-color: #23f0ff;
}

.flower-celebration-overlay .flower__light--1 {
    left: -2vmin;
    animation-delay: 1s;
}

.flower-celebration-overlay .flower__light--2 {
    left: 3vmin;
    animation-delay: 0.5s;
}

.flower-celebration-overlay .flower__light--3 {
    left: -6vmin;
    animation-delay: 0.3s;
}

.flower-celebration-overlay .flower__light--4 {
    left: 6vmin;
    animation-delay: 0.9s;
}

.flower-celebration-overlay .flower__light--5 {
    left: -1vmin;
    animation-delay: 1.5s;
}

.flower-celebration-overlay .flower__light--6 {
    left: -4vmin;
    animation-delay: 3s;
}

.flower-celebration-overlay .flower__light--7 {
    left: 3vmin;
    animation-delay: 2s;
}

.flower-celebration-overlay .flower__light--8 {
    left: -6vmin;
    animation-delay: 3.5s;
}

/* Grass Styles */
.flower-celebration-overlay .flower__grass {
    --c: #159faa;
    --line-w: 1.5vmin;
    position: absolute;
    bottom: 12vmin;
    left: -7vmin;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    z-index: 20;
    transform-origin: bottom center;
    transform: rotate(-48deg) rotateY(40deg);
}

.flower-celebration-overlay .flower__grass--1 {
    animation: moving-grass 2s linear infinite;
}

.flower-celebration-overlay .flower__grass--2 {
    left: 2vmin;
    bottom: 10vmin;
    transform: scale(0.5) rotate(75deg) rotateX(10deg) rotateY(-200deg);
    opacity: 0.8;
    z-index: 0;
    animation: moving-grass--2 1.5s linear infinite;
}

.flower-celebration-overlay .flower__grass--top {
    width: 7vmin;
    height: 10vmin;
    border-top-right-radius: 100%;
    border-right: var(--line-w) solid var(--c);
    transform-origin: bottom center;
    transform: rotate(-2deg);
}

.flower-celebration-overlay .flower__grass--bottom {
    margin-top: -2px;
    width: var(--line-w);
    height: 25vmin;
    background-image: linear-gradient(to top, transparent, var(--c));
}

.flower-celebration-overlay .flower__grass__leaf {
    --size: 10vmin;
    position: absolute;
    width: calc(var(--size) * 2.1);
    height: var(--size);
    border-top-left-radius: var(--size);
    border-top-right-radius: var(--size);
    background-image: linear-gradient(to top, transparent, transparent 30%, var(--c));
    z-index: 100;
}

.flower-celebration-overlay .flower__grass__overlay {
    position: absolute;
    top: -10%;
    right: 0%;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    filter: blur(1.5vmin);
    z-index: 100;
}

.flower-celebration-overlay .growing-grass {
    animation: growing-grass-ans 1s 2s backwards;
}

.flower__grass {
    --c: #159faa;
    --line-w: 1.5vmin;
    position: absolute;
    bottom: 12vmin;
    left: -7vmin;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    z-index: 20;
    transform-origin: bottom center;
    transform: rotate(-48deg) rotateY(40deg);
}

.flower__grass--1 {
    animation: moving-grass 2s linear infinite;
}

.flower__grass--2 {
    left: 2vmin;
    bottom: 10vmin;
    transform: scale(0.5) rotate(75deg) rotateX(10deg) rotateY(-200deg);
    opacity: 0.8;
    z-index: 0;
    animation: moving-grass--2 1.5s linear infinite;
}

.flower__grass--top {
    width: 7vmin;
    height: 10vmin;
    border-top-right-radius: 100%;
    border-right: var(--line-w) solid var(--c);
    transform-origin: bottom center;
    transform: rotate(-2deg);
}

.flower__grass--bottom {
    margin-top: -2px;
    width: var(--line-w);
    height: 25vmin;
    background-image: linear-gradient(to top, transparent, var(--c));
}

.flower__grass__leaf {
    --size: 10vmin;
    position: absolute;
    width: calc(var(--size) * 2.1);
    height: var(--size);
    border-top-left-radius: var(--size);
    border-top-right-radius: var(--size);
    background-image: linear-gradient(to top, transparent, transparent 30%, var(--c));
    z-index: 100;
}

.flower__grass__leaf--1 {
    top: -6%;
    left: 30%;
    --size: 6vmin;
    transform: rotate(-20deg);
    animation: growing-grass-ans--1 2s 2.6s backwards;
}

.flower__grass__leaf--2 {
    top: -5%;
    left: -110%;
    --size: 6vmin;
    transform: rotate(10deg);
    animation: growing-grass-ans--2 2s 2.4s linear backwards;
}

.flower__grass__leaf--3 {
    top: 5%;
    left: 60%;
    --size: 8vmin;
    transform: rotate(-18deg) rotateX(-20deg);
    animation: growing-grass-ans--3 2s 2.2s linear backwards;
}

.flower__grass__leaf--4 {
    top: 6%;
    left: -135%;
    --size: 8vmin;
    transform: rotate(2deg);
    animation: growing-grass-ans--4 2s 2s linear backwards;
}

.flower__grass__leaf--5 {
    top: 20%;
    left: 60%;
    --size: 10vmin;
    transform: rotate(-24deg) rotateX(-20deg);
    animation: growing-grass-ans--5 2s 1.8s linear backwards;
}

.flower__grass__leaf--6 {
    top: 22%;
    left: -180%;
    --size: 10vmin;
    transform: rotate(10deg);
    animation: growing-grass-ans--6 2s 1.6s linear backwards;
}

.flower__grass__leaf--7 {
    top: 39%;
    left: 70%;
    --size: 10vmin;
    transform: rotate(-10deg);
    animation: growing-grass-ans--7 2s 1.4s linear backwards;
}

.flower__grass__leaf--8 {
    top: 40%;
    left: -215%;
    --size: 11vmin;
    transform: rotate(10deg);
    animation: growing-grass-ans--8 2s 1.2s linear backwards;
}

.flower__grass__overlay {
    position: absolute;
    top: -10%;
    right: 0%;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    filter: blur(1.5vmin);
    z-index: 100;
}

@keyframes growing-grass-ans--1 {
    0% {
        transform-origin: bottom left;
        transform: rotate(-20deg) scale(0);
    }
}

@keyframes growing-grass-ans--2 {
    0% {
        transform-origin: bottom right;
        transform: rotate(10deg) scale(0);
    }
}

@keyframes growing-grass-ans--3 {
    0% {
        transform-origin: bottom left;
        transform: rotate(-18deg) rotateX(-20deg) scale(0);
    }
}

@keyframes growing-grass-ans--4 {
    0% {
        transform-origin: bottom right;
        transform: rotate(2deg) scale(0);
    }
}

@keyframes growing-grass-ans--5 {
    0% {
        transform-origin: bottom left;
        transform: rotate(-24deg) rotateX(-20deg) scale(0);
    }
}

@keyframes growing-grass-ans--6 {
    0% {
        transform-origin: bottom right;
        transform: rotate(10deg) scale(0);
    }
}

@keyframes growing-grass-ans--7 {
    0% {
        transform-origin: bottom left;
        transform: rotate(-10deg) scale(0);
    }
}

@keyframes growing-grass-ans--8 {
    0% {
        transform-origin: bottom right;
        transform: rotate(10deg) scale(0);
    }
}

@keyframes moving-grass {
    0%, 100% {
        transform: rotate(-48deg) rotateY(40deg);
    }
    50% {
        transform: rotate(-50deg) rotateY(40deg);
    }
}

@keyframes moving-grass--2 {
    0%, 100% {
        transform: scale(0.5) rotate(75deg) rotateX(10deg) rotateY(-200deg);
    }
    50% {
        transform: scale(0.5) rotate(79deg) rotateX(10deg) rotateY(-200deg);
    }
}

.growing-grass {
    animation: growing-grass-ans 1s 2s backwards;
}

@keyframes growing-grass-ans {
    0% { transform: scale(0); }
}

.flower__g-long {
    --w: 2vmin;
    --h: 6vmin;
    --c: #159faa;
    position: absolute;
    bottom: 10vmin;
    left: -3vmin;
    transform-origin: bottom center;
    transform: rotate(-30deg) rotateY(-20deg);
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    animation: flower-g-long-ans 3s linear infinite;
}

@keyframes flower-g-long-ans {
    0%, 100% {
        transform: rotate(-30deg) rotateY(-20deg);
    }
    50% {
        transform: rotate(-32deg) rotateY(-20deg);
    }
}

.flower__g-long__top {
    top: calc(var(--h) * -1);
    width: calc(var(--w) + 1vmin);
    height: var(--h);
    border-top-right-radius: 100%;
    border-right: 0.7vmin solid var(--c);
    transform: translate(-0.7vmin, 1vmin);
}

.flower__g-long__bottom {
    width: var(--w);
    height: 50vmin;
    transform-origin: bottom center;
    background-image: linear-gradient(to top, transparent 30%, var(--c));
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.5);
    clip-path: polygon(35% 0, 65% 1%, 100% 100%, 0% 100%);
}

.flower__g-right {
    position: absolute;
    bottom: 6vmin;
    left: -2vmin;
    transform-origin: bottom left;
    transform: rotate(20deg);
}

.flower__g-right .leaf {
    width: 30vmin;
    height: 50vmin;
    border-top-left-radius: 100%;
    border-left: 2vmin solid #079097;
    background-image: linear-gradient(to bottom, transparent, #000 60%);
    -webkit-mask-image: linear-gradient(to top, transparent 30%, #079097 60%);
}

.flower__g-right--1 {
    animation: flower-g-right-ans 2.5s linear infinite;
}

.flower__g-right--2 {
    left: 5vmin;
    transform: rotateY(-180deg);
    animation: flower-g-right-ans--2 3s linear infinite;
}

.flower__g-right--2 .leaf {
    height: 75vmin;
    filter: blur(0.3vmin);
    opacity: 0.8;
}

@keyframes flower-g-right-ans {
    0%, 100% {
        transform: rotate(20deg);
    }
    50% {
        transform: rotate(24deg) rotateX(-20deg);
    }
}

@keyframes flower-g-right-ans--2 {
    0%, 100% {
        transform: rotateY(-180deg) rotate(0deg) rotateX(-20deg);
    }
    50% {
        transform: rotateY(-180deg) rotate(6deg) rotateX(-20deg);
    }
}

.long-g {
    position: absolute;
    bottom: 25vmin;
    left: -42vmin;
    transform-origin: bottom left;
}

.long-g--0 {
    bottom: 0vmin;
    transform: scale(0.8) rotate(-5deg);
}

.long-g--1 {
    bottom: -3vmin;
    left: -35vmin;
    transform-origin: center;
    transform: scale(0.6) rotateX(60deg);
}

.long-g--2 {
    left: -17vmin;
    bottom: 0vmin;
    transform-origin: center;
    transform: scale(0.6) rotateX(60deg);
}

.long-g .leaf {
    --w: 15vmin;
    --h: 40vmin;
    --c: #1aaa15;
    position: absolute;
    bottom: 0;
    width: var(--w);
    height: var(--h);
    border-top-left-radius: 100%;
    border-left: 2vmin solid var(--c);
    -webkit-mask-image: linear-gradient(to top, transparent 20%, #000);
    transform-origin: bottom center;
}

.long-g .leaf--0 {
    left: 2vmin;
    animation: leaf-ans-1 4s linear infinite;
}

.long-g .leaf--1 {
    --w: 5vmin;
    --h: 60vmin;
    animation: leaf-ans-1 4s linear infinite;
}

.long-g .leaf--2 {
    --w: 10vmin;
    --h: 40vmin;
    left: -0.5vmin;
    bottom: 5vmin;
    transform-origin: bottom left;
    transform: rotateY(-180deg);
    animation: leaf-ans-2 3s linear infinite;
}

.long-g .leaf--3 {
    --w: 5vmin;
    --h: 30vmin;
    left: -1vmin;
    bottom: 3.2vmin;
    transform-origin: bottom left;
    transform: rotate(-10deg) rotateY(-180deg);
    animation: leaf-ans-3 3s linear infinite;
}

@keyframes leaf-ans-1 {
    0%, 100% {
        transform: rotate(-5deg) scale(1);
    }
    50% {
        transform: rotate(5deg) scale(1.1);
    }
}

@keyframes leaf-ans-2 {
    0%, 100% {
        transform: rotateY(-180deg) rotate(5deg);
    }
    50% {
        transform: rotateY(-180deg) rotate(0deg) scale(1.1);
    }
}

@keyframes leaf-ans-3 {
    0%, 100% {
        transform: rotate(-10deg) rotateY(-180deg);
    }
    50% {
        transform: rotate(-20deg) rotateY(-180deg);
    }
}

.grow-ans {
    animation: grow-ans 2s var(--d) backwards;
}

@keyframes grow-ans {
    0% {
        transform: scale(0);
        opacity: 0;
    }
}

/* Additional Flower Celebration Styles */
.flower-celebration-overlay .flower__g-right {
    position: absolute;
    bottom: 6vmin;
    left: -2vmin;
    transform-origin: bottom left;
    transform: rotate(20deg);
}

.flower-celebration-overlay .flower__g-right .leaf {
    width: 30vmin;
    height: 50vmin;
    border-top-left-radius: 100%;
    border-left: 2vmin solid #079097;
    background-image: linear-gradient(to bottom, transparent, #000 60%);
    -webkit-mask-image: linear-gradient(to top, transparent 30%, #079097 60%);
}

.flower-celebration-overlay .flower__g-right--1 {
    animation: flower-g-right-ans 2.5s linear infinite;
}

.flower-celebration-overlay .flower__g-right--2 {
    left: 5vmin;
    transform: rotateY(-180deg);
    animation: flower-g-right-ans--2 3s linear infinite;
}

.flower-celebration-overlay .flower__g-right--2 .leaf {
    height: 75vmin;
    filter: blur(0.3vmin);
    opacity: 0.8;
}

.flower-celebration-overlay .flower__g-front {
    position: absolute;
    bottom: 6vmin;
    left: 2.5vmin;
    z-index: 100;
    transform-origin: bottom center;
    transform: rotate(-28deg) rotateY(30deg) scale(1.04);
    animation: flower__g-front-ans 2s linear infinite;
}

.flower-celebration-overlay .flower__g-front__line {
    width: 0.3vmin;
    height: 20vmin;
    background-image: linear-gradient(to top, transparent, #079097, transparent 100%);
    position: relative;
}

.flower-celebration-overlay .flower__g-front__leaf-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    transform-origin: bottom left;
    transform: rotate(10deg);
    animation: flower__g-front__leaf-ans 1s ease-in backwards;
}

.flower-celebration-overlay .flower__g-front__leaf {
    width: 10vmin;
    height: 10vmin;
    border-radius: 100% 0% 0% 100%/100% 100% 0% 0%;
    box-shadow: inset 0 2px 1vmin rgba(44, 238, 252, 0.2);
    background-image: linear-gradient(to bottom left, transparent, #000),
                      linear-gradient(to bottom right, #159faa 50%, transparent 50%, transparent);
    -webkit-mask-image: linear-gradient(to bottom right, #159faa 50%, transparent 50%, transparent);
    mask-image: linear-gradient(to bottom right, #159faa 50%, transparent 50%, transparent);
}

.flower-celebration-overlay .flower__g-fr {
    position: absolute;
    bottom: -4vmin;
    left: 1vmin;
    transform-origin: bottom left;
    z-index: 10;
    animation: flower__g-fr-ans 2s linear infinite;
}

.flower-celebration-overlay .flower__g-fr .leaf {
    width: 30vmin;
    height: 50vmin;
    border-top-left-radius: 100%;
    border-left: 2vmin solid #079097;
    -webkit-mask-image: linear-gradient(to top, transparent 25%, #079097 50%);
    position: relative;
    z-index: 1;
}

.flower-celebration-overlay .flower__g-fr__leaf {
    position: absolute;
    top: 0;
    left: 0;
    width: 10vmin;
    height: 10vmin;
    border-radius: 100% 0% 0% 100%/100% 100% 0% 0%;
    box-shadow: inset 0 2px 1vmin rgba(44, 238, 252, 0.2);
    background-image: linear-gradient(to bottom left, transparent, #000 98%),
                      linear-gradient(to bottom right, #23f0ff 45%, transparent 50%, transparent);
    -webkit-mask-image: linear-gradient(135deg, #159faa 40%, transparent 50%, transparent);
}

.flower-celebration-overlay .long-g {
    position: absolute;
    bottom: 25vmin;
    left: -42vmin;
    transform-origin: bottom left;
}

.flower-celebration-overlay .long-g .leaf {
    --w: 15vmin;
    --h: 40vmin;
    --c: #1aaa15;
    position: absolute;
    bottom: 0;
    width: var(--w);
    height: var(--h);
    border-top-left-radius: 100%;
    border-left: 2vmin solid var(--c);
    -webkit-mask-image: linear-gradient(to top, transparent 20%, #000);
    transform-origin: bottom center;
    animation: leaf-ans-1 4s linear infinite;
}

/* Additional Keyframes for Flower Celebration */
@keyframes flower-g-right-ans {
    0%, 100% {
        transform: rotate(20deg);
    }
    50% {
        transform: rotate(24deg) rotateX(-20deg);
    }
}

@keyframes flower-g-right-ans--2 {
    0%, 100% {
        transform: rotateY(-180deg) rotate(0deg) rotateX(-20deg);
    }
    50% {
        transform: rotateY(-180deg) rotate(6deg) rotateX(-20deg);
    }
}

@keyframes flower__g-front-ans {
    0%, 100% {
        transform: rotate(-28deg) rotateY(30deg) scale(1.04);
    }
    50% {
        transform: rotate(-35deg) rotateY(40deg) scale(1.04);
    }
}

@keyframes flower__g-front__leaf-ans {
    0% {
        transform: rotate(10deg) scale(0);
    }
}

@keyframes flower__g-fr-ans {
    0%, 100% {
        transform: rotate(2deg);
    }
    50% {
        transform: rotate(4deg);
    }
}

/* Pause animations when not loaded */
.not-loaded * {
    animation-play-state: paused !important;
}
